import enum

from django.db import models


class ExternalProvider(models.TextChoices):
    GOOGLE = 'google'
    FB = 'fb'
    CRITEO = 'criteo'
    AMAZON = 'amazon'
    SYNERISE = 'synerise'
    AWIM = 'awim'

    @property
    def image_extension(self):
        """All providers can get images in WEBP, only <PERSON><PERSON><PERSON> needs JPG."""
        return {
            self.AWIM: 'jpg',
        }.get(self, 'webp')


class FileStatus(models.IntegerChoices):
    EMPTY = 1
    GENERATING = 2
    DONE = 3
    ERROR = 4


class FeedItemGeneratedMethod(models.TextChoices):
    AUTO = 'auto'
    MANUAL = 'manual'


class FeedImageType(enum.StrEnum):
    REAL_PHOTO = 'real photos'
    BLENDER = 'blender'
    WEBGL = 'webgl2'
    UNREAL = 'unreal'


class CameraSettings(enum.StrEnum):
    FRONT = 'front'
    LEFT_30 = 'left_30'
    RIGHT_30 = 'right_30'


class DoorOpening(enum.StrEnum):
    ALL_OPEN = 'allDoorsOpen'
    ALL_CLOSED = 'allClosed'
    LEFT_HALF_OPEN = 'leftHalfOpen'
