import os
from io import BytesIO

from PIL import Image
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ValidationError
from django.core.files.base import ContentFile
from django.core.validators import FileExtensionValidator
from django.db import models
from django.db.models import (
    Exists,
    OuterRef,
    Subquery,
)

from taggit.managers import TaggableManager

from custom.enums import (
    Furniture,
    LanguageEnum,
    ShelfType,
    Sofa01Color,
)
from feeds.enums import (
    ExternalProvider,
    FileStatus,
)
from feeds.image_configs import ImageConfigOption
from gallery.enums import FurnitureCategory
from gallery.models import (
    <PERSON><PERSON>,
    <PERSON><PERSON>,
)
from gallery.types import FurnitureType
from regions.constants import REGION_LANGAUGE_MAP
from regions.models import Region
from regions.services.limitations import LimitationService
from regions.types import RegionLikeObject


class Feed(models.Model):
    region = models.ForeignKey(Region, on_delete=models.CASCADE)
    categories = models.ManyToManyField(
        'feeds.FeedCategory',
        related_name='feeds',
        blank=True,
    )
    language = models.CharField(max_length=2, choices=LanguageEnum.choices)
    commerce_system = models.CharField(choices=ExternalProvider.choices, max_length=12)
    name = models.CharField(max_length=255, unique=True)
    file = models.FileField(
        upload_to='feeds/',
        null=True,
        blank=True,
    )
    file_status = models.IntegerField(
        choices=FileStatus.choices,
        default=FileStatus.EMPTY,
    )
    regenerate_automatically = models.BooleanField(default=True)

    file_created_at = models.DateTimeField(null=True, blank=True)
    file_processing_time = models.DurationField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f'Feed [{self.name} - {self.region.name}]'

    def clean(self):
        if self.language not in REGION_LANGAUGE_MAP[self.region.name]:
            raise ValidationError(
                f'Language "{self.language}" is not valid for region '
                f'"{self.region.name}"'
            )


class FeedCategory(models.Model):
    name = models.CharField(max_length=255, unique=True)
    auto_furniture_sync = models.BooleanField(
        default=False,
        help_text='If True, furniture for this category will be created and '
        'synchronized automatically.',
    )
    furniture_category = models.CharField(
        max_length=255,
        blank=True,
        choices=FurnitureCategory.choices,
        help_text='Select the relevant furniture category.',
    )
    furniture_tag = TaggableManager()

    def __str__(self):
        return f'FeedCategory [{self.name}]'


class FeedItemQuerySet(models.QuerySet):
    def all_for_region(
        self, region: RegionLikeObject | None = None
    ) -> 'FeedItemQuerySet':
        """
        Some products are not available in all regions.
        Use this method to exclude them for given region
        """
        qs = self
        if region is None:
            return qs
        limitation_service = LimitationService(region=region)

        if not limitation_service.is_s01_available:
            qs = qs.exclude(content_type__model=Furniture.sotty.value)
        elif not limitation_service.is_corduroy_available:
            corduroy_sotties = Sotty.objects.filter(
                # sotty can have only one fabric type
                # so only one value can be checked
                materials__0__in=Sofa01Color.get_corduroy_colors(),
                id=OuterRef('object_id'),
            ).values('id')[:1]
            qs = qs.exclude(
                Exists(Subquery(corduroy_sotties)),
                content_type__model=Furniture.sotty.value,
            )

        if not limitation_service.is_t03_available:
            tone_wardrobes = Watty.objects.filter(
                id=OuterRef('object_id'),
                shelf_category=FurnitureCategory.WARDROBE,
                shelf_type=ShelfType.TYPE03,
            ).values('id')[:1]
            qs = qs.exclude(
                Exists(Subquery(tone_wardrobes)),
                content_type__model=Furniture.watty.value,
            )
        return qs

    def with_images(self) -> 'FeedItemQuerySet':
        return self.filter(images__isnull=False).exclude(images__image='')


class FeedItem(models.Model):
    category = models.ForeignKey(
        FeedCategory,
        on_delete=models.CASCADE,
        related_name='items',
    )
    content_type = models.ForeignKey(
        ContentType,
        limit_choices_to=(
            models.Q(app_label='gallery', model='jetty')
            | models.Q(app_label='gallery', model='sotty')
            | models.Q(app_label='gallery', model='watty')
        ),
        on_delete=models.CASCADE,
    )
    object_id = models.PositiveIntegerField()
    furniture = GenericForeignKey('content_type', 'object_id')
    furniture_category_id = models.CharField(  # noqa: DJ001
        max_length=255,
        unique=True,
        help_text='Id build with schema:{Furniture Type Prefix (J or W)}'
        '{furniture_id}C{category_id}',
        null=True,
        db_index=True,
    )
    product_margin = models.DecimalField(
        max_digits=11, decimal_places=4, blank=True, null=True
    )

    objects = FeedItemQuerySet.as_manager()

    def __str__(self):
        return (
            f'FeedItem [{self.content_type.model} - {self.object_id} -'
            f' {self.category.name}]'
        )

    class Meta:  # noqa: DJ012
        constraints = [
            models.UniqueConstraint(
                fields=['content_type', 'object_id', 'category'],
                name='unique_content_type_object_id_category',
            )
        ]

    @staticmethod
    def get_feed_furniture_id(
        furniture: FurnitureType, feed_category: FeedCategory
    ) -> str:
        furniture_type_prefix = furniture.furniture_type[0].upper()
        return f'{furniture_type_prefix}{furniture.id}C{feed_category.id}'

    @property
    def has_lightning(self) -> bool:
        if self.content_type.model == 'watty':
            return len(self.furniture.lighting) > 0
        return False

    @property
    def image(self):
        """Return a real photo or if does not exist first unreal image."""
        return self.images.filter(
            config=ImageConfigOption.REAL_PHOTO
        ).first() or self.images.filter(
            config=ImageConfigOption.UNREAL_SCENE,
        ).first()


class FeedImage(models.Model):
    config = models.PositiveSmallIntegerField(choices=ImageConfigOption.choices)
    items = models.ManyToManyField(FeedItem, related_name='images')
    image = models.ImageField(
        upload_to='feeds/feed_image/%Y/%m',
        max_length=200,
        null=True,
        blank=True,
    )
    image_webp = models.ImageField(
        upload_to='feeds/feed_image/%Y/%m',
        max_length=200,
        null=True,
        blank=True,
        validators=[FileExtensionValidator(['webp'])],
    )
    image_jpg = models.ImageField(
        upload_to='feeds/feed_image/%Y/%m',
        max_length=200,
        null=True,
        blank=True,
        validators=[FileExtensionValidator(['jpg'])],
    )
    additional_data = models.JSONField(blank=True, default=dict)

    @property
    def file_prefix(self):
        config = ImageConfigOption(self.config)
        return config.camera_setting.name

    def save(self, *args, **kwargs):
        if self.image and not self.image_jpg:
            self.image_jpg = self._convert_image_format('JPEG')

        if self.image and not self.image_webp:
            self.image_webp = self._convert_image_format('WebP')

        super().save(*args, **kwargs)

    def _convert_image_format(self, target_format):
        """Convert the main image to the specified format."""
        if not self.image:
            return None

        with Image.open(self.image.file) as img:
            img = img.convert('RGB')
            buffer = BytesIO()
            img.save(buffer, format=target_format)
            buffer.seek(0)
            base_name = os.path.splitext(self.image.name)[0]
            extension = 'jpg' if target_format == 'JPEG' else target_format.lower()
            new_filename = f"{base_name}.{extension}"

            return ContentFile(buffer.getvalue(), name=new_filename)
