from django.db.models import Prefetch, Q, QuerySet
from typing import Optional

from catalogue.models import CatalogueEntry
from feeds.image_configs import ImageConfigOption
from feeds.models import Feed, FeedImage, FeedItem


class RealPhotoImagesService:
    def __init__(self, feed_ids: list[int]) -> None:
        self.feed_ids = feed_ids

    def _get_items_without_real_photos(self) -> QuerySet[FeedItem]:
        return FeedItem.objects.filter(
            category__feed_id__in=self.feed_ids
        ).exclude(
            images__config=ImageConfigOption.REAL_PHOTO,
            images__image__isnull=False
        ).select_related('content_type')

    @staticmethod
    def _get_catalogue_entry_with_real_photo(feed_item: FeedItem) -> CatalogueEntry | None:
        return CatalogueEntry.objects.filter(
            content_type=feed_item.content_type,
            object_id=feed_item.object_id,
            real_lifestyle_image__isnull=False,
        ).first()

    @staticmethod
    def _create_or_update_feed_image(feed_item: FeedItem, catalogue_entry: CatalogueEntry) -> None:
        if not catalogue_entry.real_lifestyle_image.name:
            return

        feed_image, created = FeedImage.objects.get_or_create(
            config=ImageConfigOption.REAL_PHOTO,
            items__content_type=feed_item.content_type,
            items__object_id=feed_item.object_id,
            defaults={'config': ImageConfigOption.REAL_PHOTO}
        )

        if created:
            feed_image.items.add(feed_item)

        if not feed_image.image.name:
            feed_image.image.name = catalogue_entry.real_lifestyle_image.name
            feed_image.save()

    def update_images_with_real_photos(self) -> None:
        feed_items = self._get_items_without_real_photos()
        for feed_item in feed_items:
            catalogue_entry = self._get_catalogue_entry_with_real_photo(feed_item)
            if catalogue_entry:
                self._create_or_update_feed_image(feed_item, catalogue_entry)
