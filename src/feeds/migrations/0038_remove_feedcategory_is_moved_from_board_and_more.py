# Generated by Django 4.2.23 on 2025-08-14 15:27

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('feeds', '0037_remove_feedcategory_image_config_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='feedcategory',
            name='is_moved_from_board',
        ),
        migrations.AddField(
            model_name='feedimage',
            name='image_jpg',
            field=models.ImageField(blank=True, max_length=200, null=True, upload_to='feeds/feed_image/%Y/%m', validators=[django.core.validators.FileExtensionValidator(['jpg'])]),
        ),
        migrations.AddField(
            model_name='feedimage',
            name='image_webp',
            field=models.ImageField(blank=True, max_length=200, null=True, upload_to='feeds/feed_image/%Y/%m', validators=[django.core.validators.FileExtensionValidator(['webp'])]),
        ),
    ]
